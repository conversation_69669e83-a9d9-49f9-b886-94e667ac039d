export type BlogPost = {
  id: string;
  title: {
    zh: string;
    en: string;
  };
  excerpt: {
    zh: string;
    en: string;
  };
  content?: {
    zh: string;
    en: string;
  };
  date: string;
  author: {
    zh: string;
    en: string;
  };
  readTime: {
    zh: string;
    en: string;
  };
  category: string;
  imageUrl: string;
  featured?: boolean;
  tags?: string[];
  seoKeywords?: {
    zh: string[];
    en: string[];
  };
};

export type BlogCategory = {
  id: string;
  name: {
    zh: string;
    en: string;
  };
};

export const categories: BlogCategory[] = [
  {
    id: 'all',
    name: {
      zh: '全部',
      en: 'All'
    }
  },
  {
    id: 'tutorials',
    name: {
      zh: '技术教程',
      en: 'Tutorials'
    }
  },
  {
    id: 'updates',
    name: {
      zh: '产品更新',
      en: 'Updates'
    }
  },
  {
    id: 'news',
    name: {
      zh: '行业资讯',
      en: 'Industry News'
    }
  },
  {
    id: 'tips',
    name: {
      zh: '使用技巧',
      en: 'Tips & Tricks'
    }
  },
  {
    id: 'security',
    name: {
      zh: '安全防护',
      en: 'Security'
    }
  },
  {
    id: 'development',
    name: {
      zh: '开发实践',
      en: 'Development'
    }
  }
];