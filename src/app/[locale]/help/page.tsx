'use client';

import { useState } from 'react';
import { ArrowLeft, Search, Plus, Minus } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

// 常见问题数据
const faqs = [
  {
    id: 1,
    question: '如何使用域名信息查询工具？',
    answer: '在域名信息查询工具页面，输入您想要查询的域名关键词（不含后缀），然后点击"查询"按钮。系统将检查多个常见顶级域名的可注册状态。绿色表示域名可能可注册，红色表示域名可能已被注册。您可以点击每个域名后的"Whois查询"链接查看更详细的注册信息。',
    category: '工具使用'
  },
  {
    id: 2,
    question: '如何使用IP地理位置查询工具？',
    answer: '在IP地理位置查询工具页面，输入有效的IP地址，然后点击"查询"按钮。系统将显示该IP地址的地理位置信息，包括国家、地区、城市等详细信息。',
    category: '工具使用'
  },
  {
    id: 3,
    question: 'DNS查询工具支持哪些记录类型？',
    answer: 'DNS查询工具支持多种记录类型，包括A记录、AAAA记录、CNAME记录、MX记录、TXT记录、NS记录等。您可以在工具页面选择需要查询的记录类型，输入域名后点击查询按钮获取结果。',
    category: '工具使用'
  },
  {
    id: 4,
    question: '网站是否免费使用？',
    answer: '是的，我们的基础工具功能完全免费使用，无需注册。我们可能会在未来推出一些高级功能，这些功能可能需要付费使用，但核心工具将始终保持免费。',
    category: '账户与付费'
  },
  {
    id: 5,
    question: '我需要注册账户才能使用工具吗？',
    answer: '大多数工具无需注册即可使用。但是，注册账户可以帮助您保存查询历史、自定义设置，并获得更好的使用体验。注册过程简单，只需要一个有效的电子邮件地址。',
    category: '账户与付费'
  },
  {
    id: 6,
    question: '如何举报网站问题或功能错误？',
    answer: '您可以通过"联系我们"页面提交问题报告，或发送邮件至**********************描述您遇到的问题。请尽可能详细地描述问题情况、复现步骤以及您使用的设备和浏览器环境，这将帮助我们更快地解决问题。',
    category: '技术支持'
  },
  {
    id: 7,
    question: '网站数据安全吗？我输入的信息会被保存吗？',
    answer: '我们非常重视用户数据安全。您使用工具输入的信息仅用于提供查询结果，不会被用于其他商业目的。我们不会长期存储敏感数据，也不会与第三方共享您的个人信息。详细信息请参阅我们的隐私政策。',
    category: '隐私安全'
  },
  {
    id: 8,
    question: 'JSON格式化工具如何使用？',
    answer: 'JSON格式化工具可以帮助您美化和验证JSON数据。只需将JSON文本粘贴到输入框中，工具会自动检测语法错误并格式化输出。您可以调整缩进空格数，也可以选择压缩JSON以减小文件大小。所有处理都在浏览器本地完成，确保数据安全。',
    category: '工具使用'
  },
  {
    id: 9,
    question: 'SEO分析工具能分析哪些内容？',
    answer: 'SEO分析工具可以全面分析网页的SEO表现，包括：标题和描述优化、关键词密度分析、图片ALT标签检查、内部和外部链接分析、页面结构评估等。工具会给出详细的评分和改进建议，帮助您优化网站的搜索引擎排名。',
    category: '工具使用'
  },
  {
    id: 10,
    question: '图片压缩工具支持哪些格式？',
    answer: '图片压缩工具支持常见的图片格式，包括JPEG、PNG、WebP等。您可以上传图片并选择压缩质量，工具会在保持视觉质量的同时减小文件大小。压缩后的图片可以直接下载使用。',
    category: '工具使用'
  },
  {
    id: 11,
    question: '如何使用加密解密工具？',
    answer: '我们提供多种加密解密工具，包括AES、DES、MD5、SHA等。选择相应的工具，输入要加密或解密的文本，设置密钥（如果需要），然后点击执行按钮。工具会显示处理结果，您可以复制或下载结果。',
    category: '工具使用'
  },
  {
    id: 12,
    question: '工具的查询结果准确吗？',
    answer: '我们的工具使用可靠的数据源和标准算法，力求提供准确的结果。但由于网络环境、数据源更新等因素，结果可能存在一定的延迟或偏差。建议将结果作为参考，重要决策请结合多个数据源进行验证。',
    category: '技术支持'
  },
  {
    id: 13,
    question: '为什么有些工具加载很慢？',
    answer: '工具加载速度可能受到网络环境、服务器负载、查询复杂度等因素影响。如果遇到加载缓慢的情况，建议：1）检查网络连接；2）刷新页面重试；3）避免在网络高峰期使用；4）如问题持续存在，请联系我们的技术支持。',
    category: '技术支持'
  },
  {
    id: 14,
    question: '可以批量处理数据吗？',
    answer: '目前大部分工具支持单个数据处理。对于有批量处理需求的用户，我们正在开发相关功能。您可以通过联系我们提出具体需求，我们会根据用户反馈优先开发最需要的批量处理功能。',
    category: '功能特性'
  },
  {
    id: 15,
    question: '工具是否支持API调用？',
    answer: '目前我们的工具主要通过网页界面提供服务。API接口正在规划中，将来可能会为开发者提供API访问方式。如果您有API集成需求，请联系我们了解最新进展和可能的解决方案。',
    category: '功能特性'
  },
  {
    id: 16,
    question: '如何保存和分享查询结果？',
    answer: '大部分工具都提供结果复制和下载功能。您可以：1）点击复制按钮将结果复制到剪贴板；2）使用下载按钮保存结果文件；3）通过浏览器的分享功能分享页面链接。某些工具还支持生成永久链接以便分享。',
    category: '功能特性'
  },
  {
    id: 8,
    question: '工具查询结果的准确性如何？',
    answer: '我们尽力提供准确的查询结果，但由于互联网数据的实时变化性，结果仅供参考。特别是域名查询、IP查询等工具，其结果可能受到网络状况、数据更新频率等因素影响。对于重要决策，建议核实多个来源的信息。',
    category: '工具使用'
  }
];

// 帮助分类
const categories = ['全部', '工具使用', '账户与付费', '技术支持', '隐私安全', '功能特性'];

export default function HelpPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale
  const [activeCategory, setActiveCategory] = useState('全部');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaqId, setExpandedFaqId] = useState<number | null>(null);

  // 切换FAQ的展开/折叠状态
  const toggleFaq = (id: number) => {
    if (expandedFaqId === id) {
      setExpandedFaqId(null);
    } else {
      setExpandedFaqId(id);
    }
  };

  // 根据分类和搜索关键词筛选FAQ
  const filteredFaqs = faqs.filter(faq => {
    const matchesCategory = activeCategory === '全部' || faq.category === activeCategory;
    const matchesSearch = !searchQuery || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.back()}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>返回首页</span>
          </button>
        </nav>

        {/* 页面标题 */}
        <header className="mb-8 text-center">
          <h1 className="text-3xl font-bold">帮助中心</h1>
          <p className="text-muted-foreground mt-2">
            寻找问题的答案，了解如何更好地使用我们的工具
          </p>
        </header>

        {/* 快速入门指南 */}
        <div className="mb-12 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">快速入门指南</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3">
                <span className="text-white font-bold">1</span>
              </div>
              <h3 className="font-medium mb-2">选择工具</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                浏览我们的工具分类，选择您需要的功能。我们提供JSON处理、SEO分析、域名查询、图像处理等多种工具。
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3">
                <span className="text-white font-bold">2</span>
              </div>
              <h3 className="font-medium mb-2">输入数据</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                根据工具要求输入相应的数据。可以是文本、文件、URL等。大部分工具都提供示例数据帮助您快速上手。
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3">
                <span className="text-white font-bold">3</span>
              </div>
              <h3 className="font-medium mb-2">获取结果</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                点击处理按钮获取结果。您可以复制结果、下载文件或直接在页面上查看。所有处理都是实时完成的。
              </p>
            </div>
          </div>
        </div>

        {/* 热门工具快速链接 */}
        <div className="mb-12">
          <h2 className="text-xl font-semibold mb-4">热门工具</h2>
          <div className="grid md:grid-cols-4 gap-4">
            <Link href="/tools/json-formatter" className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">JSON格式化</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">格式化和验证JSON数据</p>
            </Link>

            <Link href="/tools/seo-analyzer" className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">SEO分析</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">分析网页SEO优化情况</p>
            </Link>

            <Link href="/tools/domain-info" className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">域名查询</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">查询域名注册信息</p>
            </Link>

            <Link href="/tools/img-compress" className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">图片压缩</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">压缩图片减小文件大小</p>
            </Link>
          </div>
        </div>

        {/* 搜索框 */}
        <div className="mb-8">
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-muted-foreground" />
            </div>
            <input
              type="text"
              placeholder="搜索常见问题..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>

        {/* 分类导航 */}
        <div className="mb-8">
          <div className="bg-gray-50 rounded-full px-3 py-2">
            <div className="flex flex-wrap items-center justify-center gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`rounded-full px-4 py-1 text-sm transition-all ${
                    activeCategory === category
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-secondary"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 常见问题列表 */}
        <div className="space-y-4">
          {filteredFaqs.map((faq) => (
            <div key={faq.id} className="bg-card border border-border rounded-lg">
              <button
                onClick={() => toggleFaq(faq.id)}
                className="w-full flex items-center justify-between px-6 py-4"
              >
                <h3 className="font-medium text-left">{faq.question}</h3>
                <span className="text-primary ml-2">
                  {expandedFaqId === faq.id ? <Minus size={16} /> : <Plus size={16} />}
                </span>
              </button>
              {expandedFaqId === faq.id && (
                <div className="px-6 pb-4">
                  <p className="text-muted-foreground">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* 没有找到答案的提示 */}
        {filteredFaqs.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium">没有找到相关问题</h3>
            <p className="mt-2 text-muted-foreground mb-4">
              尝试使用不同的搜索词，或者直接联系我们
            </p>
            <Link href={`/${locale}/contact`} className="text-primary hover:underline">
              联系客服 →
            </Link>
          </div>
        )}

        {/* 联系支持 */}
        <div className="mt-12 text-center">
          <h2 className="text-xl font-semibold mb-4">没有找到您的问题？</h2>
          <p className="text-muted-foreground mb-6">
            如果您有任何其他问题或需要进一步帮助，请随时联系我们的客服团队
          </p>
          <Link 
            href={`/${locale}/contact`}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
          >
            联系我们
          </Link>
        </div>
      </div>
    </div>
  );
} 