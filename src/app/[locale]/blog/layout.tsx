import { Metadata } from 'next';
import { generateBlogMetadata } from '@/lib/seo/metadata';
import { generateBreadcrumbSchema } from '@/lib/seo/schema';
import Script from 'next/script';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  
  const blogData = {
    title: locale === 'zh' ? '技术博客 - 专业的开发技术分享平台' : 'Tech Blog - Professional Development Technology Sharing Platform',
    description: locale === 'zh' 
      ? '探索最新的技术趋势和开发实践。我们的博客涵盖Web开发、API设计、数据库优化、微服务架构、前端性能优化、DevOps实践等多个技术领域，为开发者提供深入的技术洞察和实用的解决方案。'
      : 'Explore the latest technology trends and development practices. Our blog covers multiple technical areas including web development, API design, database optimization, microservices architecture, frontend performance optimization, DevOps practices, providing developers with in-depth technical insights and practical solutions.',
    keywords: locale === 'zh' 
      ? ['技术博客', 'Web开发', 'API设计', '数据库优化', '微服务架构', '前端性能', 'DevOps', '开发实践', '技术教程']
      : ['tech blog', 'web development', 'API design', 'database optimization', 'microservices architecture', 'frontend performance', 'DevOps', 'development practices', 'technical tutorials'],
    url: '/blog',
    type: 'blog'
  };

  return generateBlogMetadata(blogData, locale);
}

export default function BlogLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  return (
    <>
      {children}
      <BlogSchemas params={params} />
    </>
  );
}

async function BlogSchemas({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  
  // Generate breadcrumb schema
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: locale === 'zh' ? '首页' : 'Home', url: '/' },
    { name: locale === 'zh' ? '博客' : 'Blog', url: '/blog' }
  ], locale);

  return (
    <>
      <Script
        id="blog-breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
    </>
  );
}
