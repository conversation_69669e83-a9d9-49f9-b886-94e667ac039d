'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { performSEOAnalysis, getSEOGrade } from '@/lib/seo/scoring-system';
import { SEOAnalysis } from '@/lib/seo/types';
import { Breadcrumbs, getToolBreadcrumbs } from '@/components/seo/Breadcrumbs';
import { ToolInternalLinks } from '@/components/seo/InternalLinks';

export default function SEOAnalyzerPage() {
  const locale = useLocale();
  const t = useTranslations('tools.seoAnalyzer');
  const [url, setUrl] = useState('');
  const [content, setContent] = useState('');
  const [targetKeywords, setTargetKeywords] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<SEOAnalysis | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url || !content) {
      setError(locale === 'zh' ? '请输入URL和内容' : 'Please enter URL and content');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Extract basic information from content
      const title = extractTitle(content);
      const description = extractDescription(content);
      const headings = extractHeadings(content);
      const images = extractImages(content);
      const links = extractLinks(content);
      const keywords = targetKeywords.split(',').map(k => k.trim()).filter(Boolean);
      
      // Perform SEO analysis
      const analysis = performSEOAnalysis(
        url,
        title,
        description,
        content,
        headings,
        images,
        links,
        keywords
      );
      
      setResult(analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setLoading(false);
    }
  };

  const extractTitle = (html: string): string => {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1] : '';
  };

  const extractDescription = (html: string): string => {
    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["'][^>]*>/i);
    return descMatch ? descMatch[1] : '';
  };

  const extractHeadings = (html: string): { level: number; text: string }[] => {
    const headingRegex = /<h([1-6])[^>]*>([^<]+)<\/h[1-6]>/gi;
    const headings: { level: number; text: string }[] = [];
    let match;
    
    while ((match = headingRegex.exec(html)) !== null) {
      headings.push({
        level: parseInt(match[1]),
        text: match[2].trim(),
      });
    }
    
    return headings;
  };

  const extractImages = (html: string): { src: string; alt?: string }[] => {
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*>/gi;
    const images: { src: string; alt?: string }[] = [];
    let match;
    
    while ((match = imgRegex.exec(html)) !== null) {
      images.push({
        src: match[1],
        alt: match[2] || undefined,
      });
    }
    
    return images;
  };

  const extractLinks = (html: string): { href: string; text: string; internal: boolean }[] => {
    const linkRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
    const links: { href: string; text: string; internal: boolean }[] = [];
    let match;
    
    while ((match = linkRegex.exec(html)) !== null) {
      const href = match[1];
      const isInternal = href.startsWith('/') || href.includes(new URL(url).hostname);
      
      links.push({
        href,
        text: match[2].trim(),
        internal: isInternal,
      });
    }
    
    return links;
  };

  const downloadReport = () => {
    if (result) {
      const report = {
        url,
        score: result.score,
        grade: getSEOGrade(result.score),
        issues: result.issues,
        recommendations: result.recommendations,
        metrics: result.metrics,
        analyzedAt: new Date().toISOString(),
      };
      
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = `seo-analysis-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(downloadUrl);
    }
  };

  const copyReport = () => {
    if (result) {
      const report = `SEO Analysis Report for ${url}\n\nScore: ${result.score}/100\nGrade: ${getSEOGrade(result.score).grade}\n\nIssues Found: ${result.issues.length}\nRecommendations: ${result.recommendations.length}\n\nGenerated on: ${new Date().toLocaleString()}`;
      navigator.clipboard.writeText(report);
    }
  };

  const grade = result ? getSEOGrade(result.score) : null;

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 面包屑导航 */}
        <Breadcrumbs
          items={getToolBreadcrumbs(locale === 'zh' ? 'SEO分析' : 'SEO Analyzer', locale)}
          locale={locale}
        />

        {/* Back button */}
        <button
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>{locale === 'zh' ? '返回' : 'Back'}</span>
        </button>

        {/* Tool header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">
            {locale === 'zh' ? 'SEO分析工具' : 'SEO Analyzer Tool'}
          </h1>
          <p className="text-muted-foreground mt-2">
            {locale === 'zh'
              ? '分析网页的SEO优化情况，获取详细的优化建议和评分'
              : 'Analyze webpage SEO optimization, get detailed recommendations and scoring'
            }
          </p>
        </div>

        {/* Tool description */}
        <div className="mb-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">
            {locale === 'zh' ? 'SEO分析工具 - 专业的网站SEO优化分析平台' : 'SEO Analyzer - Professional Website SEO Optimization Analysis Platform'}
          </h2>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            {locale === 'zh'
              ? 'SEO分析工具是一个功能强大的网站优化分析平台，专为网站管理员、SEO专家和数字营销人员设计。本工具可以全面分析网页的SEO表现，包括技术SEO、内容优化、关键词密度、页面结构等多个维度，并提供专业的优化建议和详细的评分报告。'
              : 'SEO Analyzer is a powerful website optimization analysis platform designed for webmasters, SEO experts, and digital marketers. This tool comprehensively analyzes webpage SEO performance, including technical SEO, content optimization, keyword density, page structure, and provides professional optimization recommendations and detailed scoring reports.'
            }
          </p>

          <div className="grid md:grid-cols-3 gap-4 mt-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <TrendingUp size={16} className="text-white" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {locale === 'zh' ? '全面分析' : 'Comprehensive Analysis'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {locale === 'zh' ? '分析标题、描述、关键词、图片、链接等SEO要素' : 'Analyze titles, descriptions, keywords, images, links and other SEO elements'}
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle size={16} className="text-white" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {locale === 'zh' ? '专业评分' : 'Professional Scoring'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {locale === 'zh' ? '基于行业标准的SEO评分系统，提供准确的优化评估' : 'Industry-standard SEO scoring system providing accurate optimization assessment'}
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                <AlertTriangle size={16} className="text-white" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {locale === 'zh' ? '优化建议' : 'Optimization Recommendations'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {locale === 'zh' ? '提供具体可行的SEO优化建议和改进方案' : 'Provide specific and actionable SEO optimization suggestions and improvement plans'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Analysis form */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="url" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '网页URL' : 'Website URL'}
              </label>
              <input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>
            
            <div>
              <label htmlFor="content" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '网页HTML内容' : 'Webpage HTML Content'}
              </label>
              <textarea
                id="content"
                placeholder={locale === 'zh' ? '粘贴网页的HTML源代码...' : 'Paste the HTML source code of the webpage...'}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-32 px-4 py-2 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-vertical"
                required
              />
            </div>
            
            <div>
              <label htmlFor="keywords" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '目标关键词（可选）' : 'Target Keywords (Optional)'}
              </label>
              <input
                id="keywords"
                type="text"
                placeholder={locale === 'zh' ? '关键词1, 关键词2, 关键词3' : 'keyword1, keyword2, keyword3'}
                value={targetKeywords}
                onChange={(e) => setTargetKeywords(e.target.value)}
                className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            
            {error && <p className="text-red-500 text-sm">{error}</p>}
            
            <button
              type="submit"
              disabled={loading}
              className="w-full h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center justify-center gap-2 disabled:opacity-70"
            >
              {loading ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  <span>{locale === 'zh' ? '分析中...' : 'Analyzing...'}</span>
                </>
              ) : (
                <>
                  <Search size={16} />
                  <span>{locale === 'zh' ? '开始分析' : 'Start Analysis'}</span>
                </>
              )}
            </button>
          </form>
        </div>

        {/* Results */}
        {result && (
          <div className="space-y-6">
            {/* Score overview */}
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">
                  {locale === 'zh' ? 'SEO评分' : 'SEO Score'}
                </h3>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={copyReport}
                    className="p-2 rounded hover:bg-accent"
                    title={locale === 'zh' ? '复制报告' : 'Copy Report'}
                  >
                    <Copy size={16} />
                  </button>
                  <button 
                    onClick={downloadReport}
                    className="p-2 rounded hover:bg-accent"
                    title={locale === 'zh' ? '下载报告' : 'Download Report'}
                  >
                    <Download size={16} />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="text-4xl font-bold" style={{ color: grade?.color }}>
                  {result.score}/100
                </div>
                <div>
                  <div className="text-2xl font-semibold" style={{ color: grade?.color }}>
                    {grade?.grade}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {grade?.description}
                  </div>
                </div>
              </div>
            </div>

            {/* Issues */}
            {result.issues.length > 0 && (
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <AlertTriangle size={20} className="text-orange-500" />
                  {locale === 'zh' ? '发现的问题' : 'Issues Found'} ({result.issues.length})
                </h3>
                <div className="space-y-3">
                  {result.issues.map((issue, index) => (
                    <div key={index} className="border-l-4 border-orange-500 pl-4">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{issue.title}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          issue.impact === 'high' ? 'bg-red-100 text-red-800' :
                          issue.impact === 'medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {issue.impact}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{issue.description}</p>
                      {issue.fix && (
                        <p className="text-sm text-blue-600">{locale === 'zh' ? '解决方案：' : 'Fix: '}{issue.fix}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {result.recommendations.length > 0 && (
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <TrendingUp size={20} className="text-green-500" />
                  {locale === 'zh' ? '优化建议' : 'Recommendations'} ({result.recommendations.length})
                </h3>
                <div className="space-y-3">
                  {result.recommendations.map((rec, index) => (
                    <div key={index} className="border-l-4 border-green-500 pl-4">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{rec.title}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                          rec.priority === 'medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {rec.priority}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{rec.description}</p>
                      <p className="text-sm text-green-600">{locale === 'zh' ? '行动：' : 'Action: '}{rec.action}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Metrics */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">
                {locale === 'zh' ? '详细指标' : 'Detailed Metrics'}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.titleLength}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '标题长度' : 'Title Length'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.descriptionLength}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '描述长度' : 'Description Length'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.headingStructure.h1Count}</div>
                  <div className="text-sm text-muted-foreground">H1 {locale === 'zh' ? '标签' : 'Tags'}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.imageOptimization.totalImages}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '图片数量' : 'Images'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* SEO Knowledge Base */}
        <div className="mt-12 grid md:grid-cols-2 gap-8">
          <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-4">
              {locale === 'zh' ? 'SEO分析维度' : 'SEO Analysis Dimensions'}
            </h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 dark:text-blue-400 text-xs">📊</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '技术SEO分析' : 'Technical SEO Analysis'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '页面加载速度、移动友好性、URL结构、网站架构等技术指标' : 'Page loading speed, mobile-friendliness, URL structure, website architecture and other technical metrics'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 dark:text-green-400 text-xs">📝</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '内容质量评估' : 'Content Quality Assessment'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '标题优化、元描述、关键词密度、内容长度和原创性分析' : 'Title optimization, meta descriptions, keyword density, content length and originality analysis'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-600 dark:text-purple-400 text-xs">🔗</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '链接结构分析' : 'Link Structure Analysis'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '内部链接、外部链接、锚文本优化和链接质量评估' : 'Internal links, external links, anchor text optimization and link quality assessment'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-orange-600 dark:text-orange-400 text-xs">🖼️</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '媒体优化检查' : 'Media Optimization Check'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '图片ALT标签、文件大小、格式优化和多媒体SEO' : 'Image ALT tags, file size, format optimization and multimedia SEO'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-4">
              {locale === 'zh' ? '使用场景' : 'Use Cases'}
            </h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '网站SEO审计' : 'Website SEO Audit'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '全面评估网站SEO现状，识别优化机会和问题' : 'Comprehensive assessment of website SEO status, identifying optimization opportunities and issues'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '竞争对手分析' : 'Competitor Analysis'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '分析竞争对手网站的SEO策略和优化水平' : 'Analyze competitors\' SEO strategies and optimization levels'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '内容优化指导' : 'Content Optimization Guidance'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '为内容创作提供SEO优化建议和关键词策略' : 'Provide SEO optimization suggestions and keyword strategies for content creation'}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <div>
                  <h3 className="font-medium mb-1">
                    {locale === 'zh' ? '定期监控' : 'Regular Monitoring'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {locale === 'zh' ? '定期检查网站SEO表现，跟踪优化效果' : 'Regularly check website SEO performance and track optimization results'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-12 bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold mb-6">
            {locale === 'zh' ? '常见问题 (FAQ)' : 'Frequently Asked Questions (FAQ)'}
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">
                {locale === 'zh' ? 'Q: SEO分析的评分标准是什么？' : 'Q: What are the scoring criteria for SEO analysis?'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {locale === 'zh'
                  ? 'A: 我们的评分系统基于Google官方SEO指南和行业最佳实践，综合考虑技术SEO、内容质量、用户体验、移动友好性等多个维度。评分范围为0-100分，90分以上为优秀，70-89分为良好，50-69分为一般，50分以下需要重点优化。'
                  : 'A: Our scoring system is based on Google\'s official SEO guidelines and industry best practices, comprehensively considering technical SEO, content quality, user experience, mobile-friendliness and other dimensions. The score ranges from 0-100, with 90+ being excellent, 70-89 being good, 50-69 being average, and below 50 requiring significant optimization.'
                }
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">
                {locale === 'zh' ? 'Q: 分析结果的准确性如何？' : 'Q: How accurate are the analysis results?'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {locale === 'zh'
                  ? 'A: 我们的分析工具基于最新的SEO算法和标准，能够准确识别大部分SEO问题。但由于搜索引擎算法的复杂性和不断更新，建议将分析结果作为优化参考，结合实际情况进行调整。同时，我们会持续更新分析算法以保持准确性。'
                  : 'A: Our analysis tool is based on the latest SEO algorithms and standards, capable of accurately identifying most SEO issues. However, due to the complexity and constant updates of search engine algorithms, we recommend using the analysis results as optimization references and making adjustments based on actual situations. We continuously update our analysis algorithms to maintain accuracy.'
                }
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">
                {locale === 'zh' ? 'Q: 如何获取网页的HTML内容？' : 'Q: How to get the HTML content of a webpage?'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {locale === 'zh'
                  ? 'A: 您可以通过以下方式获取网页HTML：1) 在浏览器中右键点击页面选择"查看页面源代码"；2) 使用浏览器开发者工具(F12)复制HTML；3) 使用curl或wget等命令行工具；4) 使用在线HTML获取工具。建议复制完整的HTML内容以获得最准确的分析结果。'
                  : 'A: You can get webpage HTML through the following methods: 1) Right-click on the page in your browser and select "View Page Source"; 2) Use browser developer tools (F12) to copy HTML; 3) Use command-line tools like curl or wget; 4) Use online HTML fetching tools. We recommend copying the complete HTML content for the most accurate analysis results.'
                }
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">
                {locale === 'zh' ? 'Q: 分析报告可以保存或分享吗？' : 'Q: Can analysis reports be saved or shared?'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {locale === 'zh'
                  ? 'A: 是的，您可以通过"下载报告"按钮将分析结果保存为JSON格式文件，也可以使用"复制报告"功能将摘要信息复制到剪贴板。这样您可以轻松地保存分析历史、与团队分享结果或进行对比分析。'
                  : 'A: Yes, you can save analysis results as a JSON file using the "Download Report" button, or use the "Copy Report" function to copy summary information to the clipboard. This allows you to easily save analysis history, share results with your team, or perform comparative analysis.'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Related Tools */}
        <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">
            {locale === 'zh' ? '相关工具推荐' : 'Related Tools'}
          </h2>
          <div className="grid md:grid-cols-3 gap-4">
            <a href="/tools/domain-info" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">
                {locale === 'zh' ? '域名信息查询' : 'Domain Information'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {locale === 'zh' ? '查询域名注册信息和DNS配置' : 'Query domain registration info and DNS configuration'}
              </p>
            </a>

            <a href="/tools/whois" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">
                {locale === 'zh' ? 'WHOIS查询' : 'WHOIS Lookup'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {locale === 'zh' ? '查询域名所有者和注册信息' : 'Query domain owner and registration information'}
              </p>
            </a>

            <a href="/tools/dns-lookup" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <h3 className="font-medium mb-2">
                {locale === 'zh' ? 'DNS查询' : 'DNS Lookup'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {locale === 'zh' ? '查询域名DNS记录和解析信息' : 'Query domain DNS records and resolution information'}
              </p>
            </a>
          </div>
        </div>

        {/* 内部链接推荐 */}
        <ToolInternalLinks locale={locale} currentTool="seo-analyzer" />
      </div>
    </div>
  );
}
