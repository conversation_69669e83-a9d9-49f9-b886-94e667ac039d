import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/seo/metadata';
import { generateToolSchema, generateBreadcrumbSchema, generateFAQSchema } from '@/lib/seo/schema';
import Script from 'next/script';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  
  const toolData = {
    name: locale === 'zh' ? 'SEO分析工具' : 'SEO Analyzer Tool',
    description: locale === 'zh' 
      ? 'SEO分析工具是一个功能强大的网站优化分析平台，专为网站管理员、SEO专家和数字营销人员设计。本工具可以全面分析网页的SEO表现，包括技术SEO、内容优化、关键词密度、页面结构等多个维度，并提供专业的优化建议和详细的评分报告。'
      : 'SEO Analyzer Tool is a powerful website optimization analysis platform designed for webmasters, SEO experts, and digital marketers. This tool comprehensively analyzes webpage SEO performance, including technical SEO, content optimization, keyword density, page structure, and provides professional optimization recommendations and detailed scoring reports.',
    keywords: locale === 'zh' 
      ? ['SEO分析', 'SEO优化', 'SEO工具', '网站分析', 'SEO检测', '搜索引擎优化', 'SEO评分', 'SEO建议']
      : ['SEO analysis', 'SEO optimization', 'SEO tool', 'website analysis', 'SEO checker', 'search engine optimization', 'SEO score', 'SEO recommendations'],
    category: locale === 'zh' ? 'SEO工具' : 'SEO Tools',
    url: '/tools/seo-analyzer',
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web Browser'
  };

  return generateToolMetadata(toolData, locale);
}

export default function SEOAnalyzerLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  return (
    <>
      {children}
      <SEOAnalyzerSchemas params={params} />
    </>
  );
}

async function SEOAnalyzerSchemas({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  
  // Generate tool schema
  const toolSchema = generateToolSchema({
    name: locale === 'zh' ? 'SEO分析工具' : 'SEO Analyzer Tool',
    description: locale === 'zh' 
      ? 'SEO分析工具可以全面分析网页的SEO表现，包括技术SEO、内容优化、关键词密度、页面结构等多个维度。'
      : 'SEO Analyzer Tool comprehensively analyzes webpage SEO performance, including technical SEO, content optimization, keyword density, page structure, and more.',
    url: '/tools/seo-analyzer',
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web Browser',
    offers: {
      price: '0',
      priceCurrency: 'USD'
    }
  }, locale);

  // Generate breadcrumb schema
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: locale === 'zh' ? '首页' : 'Home', url: '/' },
    { name: locale === 'zh' ? '工具' : 'Tools', url: '/tools' },
    { name: locale === 'zh' ? 'SEO分析' : 'SEO Analyzer', url: '/tools/seo-analyzer' }
  ], locale);

  // Generate FAQ schema
  const faqSchema = generateFAQSchema([
    {
      question: locale === 'zh' ? 'SEO分析的评分标准是什么？' : 'What are the scoring criteria for SEO analysis?',
      answer: locale === 'zh' 
        ? '我们的评分系统基于Google官方SEO指南和行业最佳实践，综合考虑技术SEO、内容质量、用户体验、移动友好性等多个维度。评分范围为0-100分，90分以上为优秀，70-89分为良好，50-69分为一般，50分以下需要重点优化。'
        : 'Our scoring system is based on Google\'s official SEO guidelines and industry best practices, comprehensively considering technical SEO, content quality, user experience, mobile-friendliness and other dimensions. The score ranges from 0-100, with 90+ being excellent, 70-89 being good, 50-69 being average, and below 50 requiring significant optimization.'
    },
    {
      question: locale === 'zh' ? '分析结果的准确性如何？' : 'How accurate are the analysis results?',
      answer: locale === 'zh' 
        ? '我们的分析工具基于最新的SEO算法和标准，能够准确识别大部分SEO问题。但由于搜索引擎算法的复杂性和不断更新，建议将分析结果作为优化参考，结合实际情况进行调整。'
        : 'Our analysis tool is based on the latest SEO algorithms and standards, capable of accurately identifying most SEO issues. However, due to the complexity and constant updates of search engine algorithms, we recommend using the analysis results as optimization references and making adjustments based on actual situations.'
    },
    {
      question: locale === 'zh' ? '如何获取网页的HTML内容？' : 'How to get the HTML content of a webpage?',
      answer: locale === 'zh' 
        ? '您可以通过以下方式获取网页HTML：1) 在浏览器中右键点击页面选择"查看页面源代码"；2) 使用浏览器开发者工具(F12)复制HTML；3) 使用curl或wget等命令行工具；4) 使用在线HTML获取工具。建议复制完整的HTML内容以获得最准确的分析结果。'
        : 'You can get webpage HTML through the following methods: 1) Right-click on the page in your browser and select "View Page Source"; 2) Use browser developer tools (F12) to copy HTML; 3) Use command-line tools like curl or wget; 4) Use online HTML fetching tools. We recommend copying the complete HTML content for the most accurate analysis results.'
    }
  ], locale);

  return (
    <>
      <Script
        id="seo-analyzer-tool-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(toolSchema),
        }}
      />
      <Script
        id="seo-analyzer-breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <Script
        id="seo-analyzer-faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
    </>
  );
}
