'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ArrowLeft, Code, Copy, Download, Trash, Upload, Check, RefreshCw, Settings } from 'lucide-react';
import { Breadcrumbs, getToolBreadcrumbs } from '@/components/seo/Breadcrumbs';
import { ToolInternalLinks } from '@/components/seo/InternalLinks';

export default function JsonFormatterPage() {
  const [jsonInput, setJsonInput] = useState<string>('');
  const [formattedJson, setFormattedJson] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [indent, setIndent] = useState<number>(2);
  const [copySuccess, setCopySuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale

  // 示例JSON数据
  const exampleJson = `{
  "name": "JSON格式化工具",
  "description": "一个简单易用的JSON美化和格式化工具",
  "features": [
    "自动格式化",
    "语法高亮",
    "可调整缩进",
    "错误提示"
  ],
  "version": 1.0,
  "isOpenSource": true,
  "metadata": {
    "createdAt": "2023-01-01",
    "updatedAt": "2023-03-15"
  }
}`;

  // 加载示例数据
  const loadExample = () => {
    setJsonInput(exampleJson);
  };

  // 清空输入
  const clearInput = () => {
    setJsonInput('');
    setFormattedJson('');
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = () => {
    if (formattedJson) {
      navigator.clipboard.writeText(formattedJson)
        .then(() => {
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  // 下载格式化后的JSON
  const downloadJson = () => {
    if (!formattedJson) return;
    
    const blob = new Blob([formattedJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'formatted-json.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setJsonInput(content);
    };
    reader.readAsText(file);
  };

  // 格式化JSON
  const formatJson = () => {
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 尝试解析JSON
      const parsedJson = JSON.parse(jsonInput);
      // 格式化JSON
      const formatted = JSON.stringify(parsedJson, null, indent);
      setFormattedJson(formatted);
    } catch (err) {
      setError(`JSON解析错误: ${err instanceof Error ? err.message : '未知错误'}`);
      setFormattedJson('');
    } finally {
      setLoading(false);
    }
  };

  // 压缩JSON (移除所有空格)
  const compressJson = () => {
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 尝试解析JSON
      const parsedJson = JSON.parse(jsonInput);
      // 压缩JSON (无缩进)
      const compressed = JSON.stringify(parsedJson);
      setFormattedJson(compressed);
    } catch (err) {
      setError(`JSON解析错误: ${err instanceof Error ? err.message : '未知错误'}`);
      setFormattedJson('');
    } finally {
      setLoading(false);
    }
  };

  // 当JSON输入变化时自动格式化
  useEffect(() => {
    if (jsonInput.trim()) {
      const timer = setTimeout(() => {
        formatJson();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [jsonInput, indent]);

  return (
    <div className="container mx-auto p-6">
      {/* 面包屑导航 */}
      <Breadcrumbs
        items={getToolBreadcrumbs(locale === 'zh' ? 'JSON格式化' : 'JSON Formatter', locale)}
        locale={locale}
      />

      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">
          {locale === 'zh' ? 'JSON格式化' : 'JSON Formatter'}
        </h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">JSON格式化工具 - 专业的JSON美化与验证工具</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          JSON格式化工具是一个功能强大的在线工具，专为开发者、数据分析师和API测试人员设计。
          本工具可以帮助您快速格式化、美化、验证和压缩JSON数据，使其更易读和易于理解。
          支持自定义缩进、实时语法错误检测、一键复制和文件下载功能。所有处理均在浏览器本地完成，确保数据安全。
        </p>

        <div className="grid md:grid-cols-3 gap-4 mt-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">1</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">实时格式化</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">输入JSON数据后自动格式化，支持自定义缩进空格数</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">2</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">语法验证</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">智能检测JSON语法错误，提供详细的错误信息</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">3</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">数据安全</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">本地处理，数据不上传服务器，保护隐私安全</p>
            </div>
          </div>
        </div>
      </div>

      {/* 缩进设置 */}
      <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center mb-2">
          <Settings size={18} className="mr-2" />
          <h2 className="text-lg font-medium">格式化设置</h2>
        </div>
        
        <div className="mb-2">
          <label className="block mb-2 text-sm font-medium">
            缩进空格数: {indent}
          </label>
          <input
            type="range"
            min="0"
            max="8"
            value={indent}
            onChange={(e) => setIndent(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>无缩进(压缩)</span>
            <span>标准(2空格)</span>
            <span>最大(8空格)</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">输入JSON</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传
                <input
                  type="file"
                  accept=".json,.txt"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          <textarea
            value={jsonInput}
            onChange={(e) => setJsonInput(e.target.value)}
            placeholder="在此粘贴或输入JSON数据..."
            className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">格式化结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={formatJson}
                disabled={loading}
                className="text-xs p-1 px-2 rounded bg-blue-500 hover:bg-blue-600 text-white flex items-center"
              >
                {loading ? <RefreshCw size={12} className="animate-spin mr-1" /> : <Code size={12} className="mr-1" />}
                格式化
              </button>
              <button
                onClick={compressJson}
                disabled={loading}
                className="text-xs p-1 px-2 rounded bg-green-500 hover:bg-green-600 text-white flex items-center"
              >
                <Code size={12} className="mr-1" />
                压缩
              </button>
              <button
                onClick={copyToClipboard}
                disabled={!formattedJson}
                className={`text-xs p-1 px-2 rounded ${
                  copySuccess 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                } flex items-center`}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制'}
              </button>
              <button
                onClick={downloadJson}
                disabled={!formattedJson}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Download size={12} className="mr-1" />
                下载
              </button>
            </div>
          </div>
          
          {error ? (
            <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 font-mono text-sm">
              {error}
            </div>
          ) : formattedJson ? (
            <pre className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm overflow-auto">
              {formattedJson}
            </pre>
          ) : (
            <div className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 flex items-center justify-center text-gray-400">
              <p>格式化后的结果将显示在这里</p>
            </div>
          )}
        </div>
      </div>

      {/* 使用场景和应用 */}
      <div className="mt-12 grid md:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold mb-4">主要应用场景</h2>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 dark:text-blue-400 text-xs">🔧</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">API开发与测试</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">格式化API响应数据，便于调试和分析接口返回结果</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-green-600 dark:text-green-400 text-xs">📊</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">数据分析</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">整理和美化JSON格式的数据文件，提高数据可读性</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-purple-600 dark:text-purple-400 text-xs">⚙️</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">配置文件管理</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">格式化应用程序配置文件，便于维护和修改</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-orange-600 dark:text-orange-400 text-xs">📝</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">代码审查</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">在代码审查过程中快速格式化JSON数据，提高审查效率</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold mb-4">工具特色功能</h2>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs">✓</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">智能错误检测</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">精确定位JSON语法错误，提供详细的错误位置和修复建议</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs">✓</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">自定义格式化</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">支持0-8个空格的自定义缩进，满足不同编码规范需求</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs">✓</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">一键压缩</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">移除所有不必要的空格和换行，最小化JSON文件大小</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs">✓</span>
              </div>
              <div>
                <h3 className="font-medium mb-1">文件导入导出</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">支持JSON文件上传和格式化结果下载，提高工作效率</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 使用指南 */}
      <div className="mt-12 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">使用指南</h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-3">如何格式化JSON</h3>
            <ol className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start space-x-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">1</span>
                <span>将JSON数据粘贴到左侧输入框中，或点击"上传"按钮选择JSON文件</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">2</span>
                <span>调整缩进设置，选择适合的空格数（推荐使用2或4个空格）</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">3</span>
                <span>工具会自动检测并格式化JSON，如有语法错误会显示详细提示</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">4</span>
                <span>点击"复制"按钮复制结果，或点击"下载"保存为文件</span>
              </li>
            </ol>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">JSON压缩说明</h3>
            <ol className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start space-x-2">
                <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">1</span>
                <span>点击"压缩"按钮可以移除所有不必要的空格和换行符</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">2</span>
                <span>压缩后的JSON文件体积更小，适合网络传输和存储</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">3</span>
                <span>压缩不会改变JSON的数据结构和内容，只是去除格式化</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs flex-shrink-0 mt-0.5">4</span>
                <span>可以随时重新格式化压缩后的JSON数据</span>
              </li>
            </ol>
          </div>
        </div>
      </div>

      {/* FAQ部分 */}
      <div className="mt-12 bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold mb-6">常见问题 (FAQ)</h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">Q: 这个工具是否安全？我的数据会被保存吗？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              A: 完全安全。我们的JSON格式化工具完全在您的浏览器本地运行，不会将任何数据发送到我们的服务器。
              您的JSON数据始终保留在您的设备上，我们无法访问或保存您的任何信息。
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">Q: 支持处理多大的JSON文件？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              A: 理论上没有大小限制，但处理速度取决于您的设备性能。一般来说，几MB的JSON文件都可以快速处理。
              对于特别大的文件，建议分批处理或使用性能更好的设备。
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">Q: 如果JSON格式有错误怎么办？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              A: 工具会自动检测JSON语法错误并显示详细的错误信息，包括错误位置和可能的原因。
              常见错误包括：缺少引号、多余的逗号、括号不匹配等。根据错误提示修正后即可正常格式化。
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">Q: 可以批量处理多个JSON文件吗？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              A: 目前工具支持单个文件处理。如需批量处理，建议将多个JSON对象合并为一个数组，
              或者逐个处理每个文件。我们正在考虑在未来版本中添加批量处理功能。
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">Q: 格式化后的JSON可以直接用于编程吗？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              A: 是的，格式化后的JSON完全符合标准规范，可以直接在任何支持JSON的编程语言和应用程序中使用。
              格式化只是改变了数据的显示方式，不会影响数据的结构和内容。
            </p>
          </div>
        </div>
      </div>

      {/* 相关工具推荐 */}
      <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">相关工具推荐</h2>
        <div className="grid md:grid-cols-3 gap-4">
          <a href="/tools/yaml-formatter" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
            <h3 className="font-medium mb-2">YAML格式化</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">格式化和验证YAML配置文件</p>
          </a>

          <a href="/tools/json-to-yaml" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
            <h3 className="font-medium mb-2">JSON转YAML</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">将JSON数据转换为YAML格式</p>
          </a>

          <a href="/tools/json-to-csv" className="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
            <h3 className="font-medium mb-2">JSON转CSV</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">将JSON数据转换为CSV表格格式</p>
          </a>
        </div>
      </div>

      {/* 内部链接推荐 */}
      <ToolInternalLinks locale={locale} currentTool="json-formatter" />
    </div>
  );
}