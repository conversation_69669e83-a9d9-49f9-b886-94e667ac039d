import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/seo/metadata';
import { generateToolSchema, generateBreadcrumbSchema, generateFAQSchema } from '@/lib/seo/schema';
import Script from 'next/script';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  
  const toolData = {
    name: locale === 'zh' ? 'JSON格式化工具' : 'JSON Formatter Tool',
    description: locale === 'zh' 
      ? 'JSON格式化工具是一个功能强大的在线工具，专为开发者、数据分析师和API测试人员设计。本工具可以帮助您快速格式化、美化、验证和压缩JSON数据，使其更易读和易于理解。支持自定义缩进、实时语法错误检测、一键复制和文件下载功能。所有处理均在浏览器本地完成，确保数据安全。'
      : 'JSON Formatter Tool is a powerful online tool designed for developers, data analysts, and API testers. This tool helps you quickly format, beautify, validate, and compress JSON data, making it more readable and understandable. Supports custom indentation, real-time syntax error detection, one-click copy, and file download functions. All processing is completed locally in the browser to ensure data security.',
    keywords: locale === 'zh' 
      ? ['JSON格式化', 'JSON美化', 'JSON验证', 'JSON压缩', 'JSON工具', '在线JSON格式化', 'JSON解析器', 'JSON编辑器']
      : ['JSON formatter', 'JSON beautifier', 'JSON validator', 'JSON compressor', 'JSON tool', 'online JSON formatter', 'JSON parser', 'JSON editor'],
    category: locale === 'zh' ? '开发工具' : 'Development Tools',
    url: '/tools/json-formatter',
    applicationCategory: 'DeveloperApplication',
    operatingSystem: 'Web Browser'
  };

  return generateToolMetadata(toolData, locale);
}

export default function JsonFormatterLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  return (
    <>
      {children}
      <JsonFormatterSchemas params={params} />
    </>
  );
}

async function JsonFormatterSchemas({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  
  // Generate tool schema
  const toolSchema = generateToolSchema({
    name: locale === 'zh' ? 'JSON格式化工具' : 'JSON Formatter Tool',
    description: locale === 'zh' 
      ? 'JSON格式化工具可以帮助您格式化、美化和验证JSON数据，使其更易读和易于理解。'
      : 'JSON Formatter Tool helps you format, beautify, and validate JSON data, making it more readable and understandable.',
    url: '/tools/json-formatter',
    applicationCategory: 'DeveloperApplication',
    operatingSystem: 'Web Browser',
    offers: {
      price: '0',
      priceCurrency: 'USD'
    }
  }, locale);

  // Generate breadcrumb schema
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: locale === 'zh' ? '首页' : 'Home', url: '/' },
    { name: locale === 'zh' ? '工具' : 'Tools', url: '/tools' },
    { name: locale === 'zh' ? 'JSON格式化' : 'JSON Formatter', url: '/tools/json-formatter' }
  ], locale);

  // Generate FAQ schema
  const faqSchema = generateFAQSchema([
    {
      question: locale === 'zh' ? '这个工具是否安全？我的数据会被保存吗？' : 'Is this tool safe? Will my data be saved?',
      answer: locale === 'zh' 
        ? '完全安全。我们的JSON格式化工具完全在您的浏览器本地运行，不会将任何数据发送到我们的服务器。您的JSON数据始终保留在您的设备上，我们无法访问或保存您的任何信息。'
        : 'Completely safe. Our JSON formatting tool runs entirely locally in your browser and does not send any data to our servers. Your JSON data always remains on your device, and we cannot access or save any of your information.'
    },
    {
      question: locale === 'zh' ? '支持处理多大的JSON文件？' : 'What size JSON files are supported?',
      answer: locale === 'zh' 
        ? '理论上没有大小限制，但处理速度取决于您的设备性能。一般来说，几MB的JSON文件都可以快速处理。对于特别大的文件，建议分批处理或使用性能更好的设备。'
        : 'There is no theoretical size limit, but processing speed depends on your device performance. Generally, JSON files of several MB can be processed quickly. For particularly large files, it is recommended to process in batches or use a more powerful device.'
    },
    {
      question: locale === 'zh' ? '如果JSON格式有错误怎么办？' : 'What if there are errors in the JSON format?',
      answer: locale === 'zh' 
        ? '工具会自动检测JSON语法错误并显示详细的错误信息，包括错误位置和可能的原因。常见错误包括：缺少引号、多余的逗号、括号不匹配等。根据错误提示修正后即可正常格式化。'
        : 'The tool will automatically detect JSON syntax errors and display detailed error information, including error location and possible causes. Common errors include: missing quotes, extra commas, mismatched brackets, etc. You can format normally after correcting according to the error prompts.'
    }
  ], locale);

  return (
    <>
      <Script
        id="json-formatter-tool-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(toolSchema),
        }}
      />
      <Script
        id="json-formatter-breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <Script
        id="json-formatter-faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
    </>
  );
}
