import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface InternalLink {
  href: string;
  title: string;
  description: string;
  category: string;
}

interface InternalLinksProps {
  locale: string;
  currentPage?: string;
  maxLinks?: number;
}

export function InternalLinks({ locale, currentPage, maxLinks = 6 }: InternalLinksProps) {
  const pathname = usePathname();
  
  // Define internal links for better SEO
  const allLinks: InternalLink[] = [
    // Tools
    {
      href: `/tools/json-formatter`,
      title: locale === 'zh' ? 'JSON格式化工具' : 'JSON Formatter',
      description: locale === 'zh' ? '格式化和验证JSON数据' : 'Format and validate JSON data',
      category: 'tools'
    },
    {
      href: `/tools/seo-analyzer`,
      title: locale === 'zh' ? 'SEO分析工具' : 'SEO Analyzer',
      description: locale === 'zh' ? '分析网页SEO优化情况' : 'Analyze webpage SEO optimization',
      category: 'tools'
    },
    {
      href: `/tools/domain-info`,
      title: locale === 'zh' ? '域名信息查询' : 'Domain Information',
      description: locale === 'zh' ? '查询域名注册和DNS信息' : 'Query domain registration and DNS info',
      category: 'tools'
    },
    {
      href: `/tools/ip-location`,
      title: locale === 'zh' ? 'IP地理位置查询' : 'IP Geolocation',
      description: locale === 'zh' ? '查询IP地址的地理位置信息' : 'Query IP address geolocation information',
      category: 'tools'
    },
    {
      href: `/tools/img-compress`,
      title: locale === 'zh' ? '图片压缩工具' : 'Image Compressor',
      description: locale === 'zh' ? '压缩图片减小文件大小' : 'Compress images to reduce file size',
      category: 'tools'
    },
    {
      href: `/tools/yaml-formatter`,
      title: locale === 'zh' ? 'YAML格式化工具' : 'YAML Formatter',
      description: locale === 'zh' ? '格式化和验证YAML配置文件' : 'Format and validate YAML configuration files',
      category: 'tools'
    },
    
    // Blog posts
    {
      href: `/blog/comprehensive-api-testing-guide-2024`,
      title: locale === 'zh' ? 'API测试完整指南' : 'Comprehensive API Testing Guide',
      description: locale === 'zh' ? '从基础到高级的全面测试策略' : 'Complete testing strategies from basic to advanced',
      category: 'blog'
    },
    {
      href: `/blog/modern-web-security-best-practices-2024`,
      title: locale === 'zh' ? '现代Web安全最佳实践' : 'Modern Web Security Best Practices',
      description: locale === 'zh' ? '全面防护指南' : 'Comprehensive protection guide',
      category: 'blog'
    },
    {
      href: `/blog/database-optimization-performance-tuning-guide`,
      title: locale === 'zh' ? '数据库性能优化指南' : 'Database Performance Optimization Guide',
      description: locale === 'zh' ? '从查询优化到架构设计' : 'From query optimization to architecture design',
      category: 'blog'
    },
    
    // Static pages
    {
      href: `/help`,
      title: locale === 'zh' ? '帮助中心' : 'Help Center',
      description: locale === 'zh' ? '常见问题和使用指南' : 'FAQ and user guides',
      category: 'help'
    },
    {
      href: `/about`,
      title: locale === 'zh' ? '关于我们' : 'About Us',
      description: locale === 'zh' ? '了解我们的使命和团队' : 'Learn about our mission and team',
      category: 'about'
    },
    {
      href: `/blog`,
      title: locale === 'zh' ? '技术博客' : 'Tech Blog',
      description: locale === 'zh' ? '最新技术文章和教程' : 'Latest tech articles and tutorials',
      category: 'blog'
    }
  ];

  // Filter out current page and select relevant links
  const relevantLinks = allLinks
    .filter(link => !pathname.includes(link.href))
    .slice(0, maxLinks);

  if (relevantLinks.length === 0) {
    return null;
  }

  return (
    <div className="mt-12 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
      <h2 className="text-xl font-semibold mb-4">
        {locale === 'zh' ? '相关推荐' : 'Related Links'}
      </h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {relevantLinks.map((link) => (
          <Link
            key={link.href}
            href={`/${locale}${link.href}`}
            className="block p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow"
          >
            <h3 className="font-medium mb-2 text-gray-900 dark:text-gray-100">
              {link.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {link.description}
            </p>
          </Link>
        ))}
      </div>
    </div>
  );
}

// Specific internal links for different page types
export function ToolInternalLinks({ locale, currentTool }: { locale: string; currentTool?: string }) {
  return <InternalLinks locale={locale} currentPage={currentTool} maxLinks={6} />;
}

export function BlogInternalLinks({ locale, currentPost }: { locale: string; currentPost?: string }) {
  return <InternalLinks locale={locale} currentPage={currentPost} maxLinks={6} />;
}

export function PageInternalLinks({ locale, currentPage }: { locale: string; currentPage?: string }) {
  return <InternalLinks locale={locale} currentPage={currentPage} maxLinks={8} />;
}
