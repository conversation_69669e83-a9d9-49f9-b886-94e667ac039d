import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  name: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  locale: string;
}

export function Breadcrumbs({ items, locale }: BreadcrumbsProps) {
  return (
    <nav aria-label="Breadcrumb" className="mb-6">
      <ol className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
        <li>
          <Link
            href={`/${locale}`}
            className="flex items-center hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
          >
            <Home size={16} className="mr-1" />
            <span className="sr-only">{locale === 'zh' ? '首页' : 'Home'}</span>
          </Link>
        </li>
        
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            <ChevronRight size={16} className="mx-2 text-gray-400" />
            {item.href && !item.current ? (
              <Link
                href={`/${locale}${item.href}`}
                className="hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
              >
                {item.name}
              </Link>
            ) : (
              <span
                className={item.current ? 'text-gray-900 dark:text-gray-200 font-medium' : ''}
                aria-current={item.current ? 'page' : undefined}
              >
                {item.name}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Predefined breadcrumb generators for common page types
export function getToolBreadcrumbs(toolName: string, locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '工具' : 'Tools', href: '/tools' },
    { name: toolName, current: true }
  ];
}

export function getBlogBreadcrumbs(postTitle: string, locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '博客' : 'Blog', href: '/blog' },
    { name: postTitle, current: true }
  ];
}

export function getBlogCategoryBreadcrumbs(categoryName: string, locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '博客' : 'Blog', href: '/blog' },
    { name: categoryName, current: true }
  ];
}

export function getHelpBreadcrumbs(locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '帮助中心' : 'Help Center', current: true }
  ];
}

export function getAboutBreadcrumbs(locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '关于我们' : 'About Us', current: true }
  ];
}

export function getContactBreadcrumbs(locale: string): BreadcrumbItem[] {
  return [
    { name: locale === 'zh' ? '联系我们' : 'Contact Us', current: true }
  ];
}
